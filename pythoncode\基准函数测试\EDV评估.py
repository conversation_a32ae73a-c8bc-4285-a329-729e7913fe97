from ast import Set
from re import S
import networkx as nx
import numpy as np
import time


class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e


# def EDV(graph, S, p):
#     """
#     优化后的影响力评估 (Expected Diffusion Value)
    
#     Args:
#         graph: NetworkX图对象
#         S: 种子节点集合
#         p: 传播概率
        
#     Returns:
#         float: 估计的影响力值
#     """
#     # 预生成邻接字典（将邻居存储为集合）
#     adj_dict = graph.neighbors
#     S = set(S)
    
#     # 计算一阶邻居 (NS_1)，直接使用集合操作
#     NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
#     # 快速计算每个邻居的连接数
#     influence_sum = 0
#     for node in NS_1:
#         num_connections = len(set(adj_dict[node]) & S)
#         influence_sum += 1 - (1 - p) ** num_connections
    
#     return len(S) + influence_sum


def EDV(graph, S, p):

    """向量化优化的EDV计算，无需显式循环"""
    # 生成节点索引映射（应对非连续节点ID）
    nodes = list(graph.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n = len(nodes)
    
    # 构建稀疏邻接矩阵（CSR格式加速矩阵运算）
    adj_csr = nx.adjacency_matrix(graph, nodelist=nodes).astype(bool).tocsr()
    
    # 生成种子节点掩码向量（O(1)时间访问）
    seed_indices = np.array([node_index[s] for s in S if s in node_index], dtype=np.int32)
    if len(seed_indices) == 0:
        return 0.0
    seed_mask = np.zeros(n, dtype=np.int8)
    seed_mask[seed_indices] = 1
    
    # 单次矩阵乘法获取连接数（替代双重循环）
    conn_counts = adj_csr[:, seed_indices].sum(axis=1)  # 移除 .A1
    
    # 计算一跳邻居（排除种子节点）
    one_hop_mask = (conn_counts > 0) & (seed_mask == 0)
    one_hop_counts = conn_counts[one_hop_mask]
    
    # 向量化概率计算
    influence = np.sum(1 - np.power(1 - p, one_hop_counts))
    
    return len(seed_indices) + influence


def EDV_with_2nd_order(graph, S, p, alpha=1.0, beta=1.0):
    """
    二阶邻居传播受一阶邻居激活约束的条件概率版EDV计算
    """
    S = set(S)
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes}
    
    NS_1 = set()
    for s in S:
        NS_1.update(adj_dict.get(s, set()))
    NS_1 -= S

    p_1_dict = {}
    influence_1 = 0.0
    for node in NS_1:
        num_connections = len(adj_dict[node] & S)
        p_activated = 1 - (1 - p) ** num_connections
        p_1_dict[node] = p_activated
        influence_1 += p_activated

    NS_2 = set()
    for n1 in NS_1:
        NS_2.update(adj_dict[n1])
    NS_2 -= S
    NS_2 -= NS_1

    influence_2 = 0.0
    for node in NS_2:
        first_neighbors = adj_dict[node] & NS_1
        product_term = 1.0
        for fn in first_neighbors:
            # 条件概率计算：P(u激活且传播到w失败) = 1 - P_1(u) * p
            product_term *= (1 - p * p_1_dict.get(fn, 0))
        p_activated_2 = 1 - product_term
        influence_2 += p_activated_2

    total_influence = len(S) + alpha * influence_1 + beta * influence_2
    return total_influence

def EDV_with_3rd_order_conditional(graph, S, p, alpha=1.0, beta=1.0, gamma=1.0):
    """
    三阶邻居传播，严格条件概率链式传播版EDV计算
    """
    S = set(S)
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes}

    # 一阶邻居
    NS_1 = set()
    for s in S:
        NS_1.update(adj_dict.get(s, set()))
    NS_1 -= S

    p_1_dict = {}
    influence_1 = 0.0
    for node in NS_1:
        num_connections = len(adj_dict[node] & S)
        p_activated = 1 - (1 - p) ** num_connections
        p_1_dict[node] = p_activated
        influence_1 += p_activated

    # 二阶邻居
    NS_2 = set()
    for n1 in NS_1:
        NS_2.update(adj_dict[n1])
    NS_2 -= S
    NS_2 -= NS_1

    p_2_dict = {}
    influence_2 = 0.0
    for node in NS_2:
        first_neighbors = adj_dict[node] & NS_1
        product_term = 1.0
        for fn in first_neighbors:
            product_term *= (1 - p * p_1_dict.get(fn, 0))
        p_activated_2 = 1 - product_term
        p_2_dict[node] = p_activated_2
        influence_2 += p_activated_2

    # 三阶邻居
    NS_3 = set()
    for n2 in NS_2:
        NS_3.update(adj_dict[n2])
    NS_3 -= S
    NS_3 -= NS_1
    NS_3 -= NS_2

    influence_3 = 0.0
    for node in NS_3:
        second_neighbors = adj_dict[node] & NS_2
        product_term = 1.0
        for sn in second_neighbors:
            product_term *= (1 - p * p_2_dict.get(sn, 0))
        p_activated_3 = 1 - product_term
        influence_3 += p_activated_3

    total_influence = len(S) + alpha * influence_1 + beta * influence_2 + gamma * influence_3
    return total_influence


def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数
        
    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)
    
    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]
    
    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)
    
    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)
    
    # 批量生成随机数（优化随机数生成效率）
    rand_pool = np.random.rand(mc, n*5)  # 预生成随机数池（5倍冗余）
    pool_ptr = 0
    
    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()
        
        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])
            
            if neighbors.size == 0:
                break
            
            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数
            
            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)
            
            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]
            
            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True
        
        influence[i] = active.sum()
    
    return np.mean(influence)


def main():
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # G = gen_graph(network_path)
    G = GEN_GRAPH(network_path)
    p = 0.05
    k = 100  # 设置要选择的顶点数量
    
    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(), 
                         key=lambda x: x[1], 
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    # top_k_nodes =   [1137, 149, 6, 58, 1759, 1571, 1719, 8892, 9, 114, 19, 84, 17, 2, 560, 428, 250, 8896, 502, 148, 69, 2847, 558, 191, 3730, 60, 22, 799, 61, 1667, 48, 5, 1321, 1442, 853, 15, 405, 1020, 689, 1062, 374, 562, 182, 13, 728, 824, 1064, 304, 1583, 218]
    

    start_time = time.time()
    edv_optimized = EDV(G, top_k_nodes, p)
    edv_opt_time = time.time() - start_time
    
    # 计算带二阶邻居的优化EDV并统计时间
    start_time = time.time()
    edv_with_2nd = EDV_with_2nd_order(G.nx_G, top_k_nodes, p)
    edv_with_2nd_time = time.time() - start_time
    
    start_time = time.time()
    edv_with_3rd = EDV_with_3rd_order_conditional(G.nx_G, top_k_nodes, p)
    edv_with_3rd_time = time.time() - start_time


    
    # 计算IC模型模拟结果
    start_time = time.time()
    ic_result = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)
    ic_time = time.time() - start_time

    # 打印计算结果和时间统计
    print("计算结果和时间统计:")

    print(f"优化后的EDV: {edv_optimized} (耗时: {edv_opt_time:.10f}秒)")
    print(f"带二阶邻居的优化EDV: {edv_with_2nd} (耗时: {edv_with_2nd_time:.10f}秒)")
    print(f"带三阶邻居的优化EDV: {edv_with_3rd} (耗时: {edv_with_3rd_time:.10f}秒)")
    print(f"IC模型模拟 (1000次): {ic_result} (耗时: {ic_time:.10f}秒)")
    
    
    
    # 与评估函数的准确度比较
    print("\n各评估函数与IC模拟的相对误差:")
    if ic_result > 0:
        print(f"优化EDV相对误差: {abs((edv_optimized - ic_result)/ic_result)*100:.2f}%")
        print(f"带二阶邻居的EDV相对误差: {abs((edv_with_2nd - ic_result)/ic_result)*100:.2f}%")
        print(f"带三阶邻居的EDV相对误差: {abs((edv_with_3rd - ic_result)/ic_result)*100:.2f}%")

if __name__ == "__main__":
    main()