def PRE_strict(G, S, p, max_hop=5):
    """
    基于严格概率递推的影响力估计（仅支持无向图）
    Args:
        S: 种子节点集合
        G: networkx无向图对象
        p: 传播概率
        max_hop: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    # 初始化激活概率向量
    P = {v: 1.0 if v in S else 0.0 for v in nodes}
    for _ in range(max_hop):
        new_P = {}
        for v in nodes:
            if v in S:
                new_P[v] = 1.0
                continue
            neighbors = list(G.neighbors(v))
            prob_not_activated = 1.0
            for u in neighbors:
                prob_not_activated *= (1 - p * P[u])
            new_P[v] =  (1 - P[v]) * (1 - prob_not_activated)

        P = new_P
    return sum(P.values())
import networkx as nx
import numpy as np
import time


class GEN_GRAPH:
    """Network graph generator and neighbor cache container

    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """

    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存

        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）

        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）

        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）

        Returns:
            生成的NetworkX无向图对象

        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e

# def PRE(G, S, p, max_hop=5):
#     """
#     BP近似影响力估计（全节点递推）
#     Args:
#         S: 种子节点集合
#         G: 网络X图对象
#         p: 传播概率
#         max_iter: 最大递推轮数
#     Returns:
#         float: 估计影响力值
#     """
#     S = set(S)
#     nodes = list(G.nx_G.nodes())
#     # 初始化激活概率
#     P = {v: 1.0 if v in S else 0.0 for v in nodes}

#     for _ in range(max_hop):
#         new_P = P.copy()
#         for v in nodes:
#             if v in S:
#                 continue
#             parents = list(G.nx_G.neighbors(v))
#             prob_not = 1.0
#             for u in parents:
#                 prob_not *= 1 - p * P[u]

#             new_P[v] = 1 - prob_not
#         P = new_P
#     return sum(P.values())


import math

def PRE(G, S, p, max_hop=5):
    """
    BP近似影响力估计（全节点递推）
    使用对数转换优化连乘计算
    Args:
        S: 种子节点集合
        G: 网络X图对象
        p: 传播概率
        max_iter: 最大递推轮数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nx_G.nodes())
    # 初始化激活概率
    P = {v: 1.0 if v in S else 0.0 for v in nodes}

    for _ in range(max_hop):
        new_P = P.copy()
        for v in nodes:
            if v in S:
                continue
            parents = list(G.nx_G.neighbors(v))
            
            # 计算对数形式的未激活概率
            log_prob_not = 0.0  # 初始化为log(1.0) = 0
            
            for u in parents:
                factor = 1 - p * P[u]
                
                # 处理边界情况，避免log(0)
                if factor < 1e-20:  # 接近0时取极小值
                    factor = 1e-20
                
                log_prob_not += math.log(factor)
            
            # 转换回概率空间
            prob_not = math.exp(log_prob_not)
            new_P[v] = 1 - prob_not
        
        P = new_P
    
    return sum(P.values())



def BP_Neumann(G, S, p, alpha=0.1, K=3):
    """
    Neumann级数修正的BP近似影响力估计函数
    Args:
        G: 网络X图对象
        S: 种子节点集合
        p: 传播概率
        alpha: Neumann修正强度因子
        K: Neumann级数截断阶数
    Returns:
        float: 估计影响力值
    """
    S = set(S)
    nodes = list(G.nodes())
    idx_map = {v: i for i, v in enumerate(nodes)}
    n = len(nodes)

    # 构建传播矩阵 A
    A = np.zeros((n, n))
    for u, v in G.edges():
        A[idx_map[u], idx_map[v]] = p
        A[idx_map[v], idx_map[u]] = p  # 若为无向图

    # 初始化激活概率向量
    P = np.array([1.0 if v in S else 0.0 for v in nodes])

    max_hop = 4
    history = [P.copy()]

    for t in range(1, max_hop + 1):
        new_P = P.copy()
        for i, v in enumerate(nodes):
            if v in S:
                continue
            parents = list(G.neighbors(v))
            prob_not = 1.0
            for u in parents:
                prob_not *= 1 - p * P[idx_map[u]]
            new_P[i] = 1 - prob_not
        P = new_P
        history.append(P.copy())

        # Neumann级数修正
        correction = np.zeros_like(P)
        for k in range(2, min(K + 1, len(history))):
            Ak = np.linalg.matrix_power(A, k)
            correction += Ak @ history[-k]
        P += alpha * correction
        P = np.clip(P, 0, 1)  # 保证概率合法

    return float(np.sum(P))


def IC_vec(g, seed, p, mc=1000):
    """向量化实现的独立级联模型模拟

    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: 蒙特卡洛模拟次数

    Returns:
        float: 平均影响力扩散范围
    """
    # 预处理节点到索引的映射和邻居列表
    node_list = list(g.nodes())
    node_to_index = {node: idx for idx, node in enumerate(node_list)}
    n = len(node_list)

    # 预存每个节点的邻居索引数组（向量化预生成）
    preprocessed_neighbors = [
        np.array([node_to_index[n] for n in g.neighbors(node)], dtype=np.int32)
        for node in node_list
    ]

    # 转换种子节点为索引
    seed_indices = np.array([node_to_index[n] for n in seed], dtype=np.int32)

    # 预分配内存空间
    influence = np.empty(mc, dtype=np.int32)

    for i in range(mc):
        active = np.zeros(n, dtype=np.bool_)
        active[seed_indices] = True
        newly_active = active.copy()

        while np.any(newly_active):
            # 向量化收集激活节点的所有邻居
            current_active = np.flatnonzero(newly_active)
            neighbors = np.concatenate([preprocessed_neighbors[idx] for idx in current_active])

            if neighbors.size == 0:
                break

            # 生成与neighbors长度匹配的随机数
            rand_values = np.random.rand(len(neighbors))  # 直接生成所需长度的随机数

            # 向量化筛选激活节点
            activated = neighbors[rand_values < p]
            unique_activated = np.unique(activated)

            # 更新激活状态
            is_new = ~active[unique_activated]
            newly_active_nodes = unique_activated[is_new]

            active[newly_active_nodes] = True
            newly_active.fill(False)
            newly_active[newly_active_nodes] = True

        influence[i] = active.sum()

    return np.mean(influence)




def main():
    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"


    G = GEN_GRAPH(network_path)
    p = 0.05
    k = 100  # 设置要选择的顶点数量

    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G.nx_G)
    sorted_nodes = sorted(degree_centrality.items(),
                         key=lambda x: x[1],
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    print("top k nodes 计算完成")

    # 计算BP近似并统计时间（使用更精确的计时器）
    start_time = time.perf_counter()
    bp_result = PRE(G, top_k_nodes, p)
    bp_time = time.perf_counter() - start_time

    # 计算严格概率递推PRE_strict并统计时间
    start_time = time.perf_counter()
    pre_strict_result = PRE_strict(G.nx_G, top_k_nodes, p)
    pre_strict_time = time.perf_counter() - start_time

    # # 计算BP Neumann修正并统计时间
    # start_time = time.perf_counter()
    # bp_neumann_result = BP_Neumann(G.nx_G, top_k_nodes, p, alpha=0.1, K=3)
    # bp_neumann_time = time.perf_counter() - start_time

    # 计算IC模型模拟结果
    start_time = time.perf_counter()
    ic_result = IC_vec(G.nx_G, top_k_nodes, p, mc=10000)
    ic_time = time.perf_counter() - start_time

    # 打印计算结果和时间统计
    print("计算结果和时间统计:")
    print(f"BP近似: {bp_result} (耗时: {bp_time:.10f}秒)")
    print(f"PRE严格概率递推: {pre_strict_result} (耗时: {pre_strict_time:.10f}秒)")
    # print(f"BP Neumann修正: {bp_neumann_result} (耗时: {bp_neumann_time:.10f}秒)")
    print(f"IC模型模拟 (10000次): {ic_result} (耗时: {ic_time:.10f}秒)")

    # 与IC模拟的准确度比较
    print("\n各评估函数与IC模拟的相对误差:")
    if ic_result > 0:
        print(f"BP近似相对误差: {abs((bp_result - ic_result)/ic_result)*100:.2f}%")
    print(f"PRE严格概率递推相对误差: {abs((pre_strict_result - ic_result)/ic_result)*100:.2f}%")
        # print(f"BP Neumann修正相对误差: {abs((bp_neumann_result - ic_result)/ic_result)*100:.2f}%")

    # 计算性能提升
    print("\n性能分析:")
    if bp_time > 0:
        print(f"BP近似相比IC模拟速度提升: {(ic_time/bp_time):.2f}倍")
    else:
        print("BP近似执行时间过短，无法准确计算速度提升比例")
    if pre_strict_time > 0:
        print(f"PRE严格概率递推相比IC模拟速度提升: {(ic_time/pre_strict_time):.2f}倍")
    else:
        print("PRE严格概率递推执行时间过短，无法准确计算速度提升比例")

    # if bp_neumann_time > 0:
        # print(f"BP Neumann修正相比IC模拟速度提升: {(ic_time/bp_neumann_time):.2f}倍")
    # else:
        # print("BP Neumann修正执行时间过短，无法准确计算速度提升比例")

    # print(f"BP Neumann修正相比BP近似精度提升: {abs((bp_neumann_result - ic_result) - (bp_result - ic_result)):.2f}")

    # 测试不同参数对BP Neumann的影响
    # print("\n不同参数下的BP Neumann结果:")
    # for alpha in [0.05, 0.1, 0.2]:
    #     for K in [2, 3, 5]:
    #         start_time = time.perf_counter()
    #         result = BP_Neumann(G.nx_G, top_k_nodes, p, alpha=alpha, K=K)
    #         elapsed_time = time.perf_counter() - start_time
    #         error = abs((result - ic_result)/ic_result)*100 if ic_result > 0 else 0
    #         print(f"  alpha={alpha}, K={K}: {result:.2f} (误差: {error:.2f}%, 耗时: {elapsed_time:.6f}秒)")

if __name__ == "__main__":
    main()